FROM ubuntu:22.04

RUN apt-get update && apt-get upgrade -y && apt-get install -y \
    sqlite3 \
    openssl \
    apache2-utils \
    curl \
    netcat \
    unzip \
    sudo \
    systemctl \
    nano \
    net-tools \
    logrotate \
    jq \
    bc \
    && rm -rf /var/lib/apt/lists/*

RUN useradd -d /var/lib/rport -m -U -r -s /bin/false rport \
    && mkdir -p /etc/rport /var/log/rport /tmp/rport/docroot

RUN adduser rport sudo

RUN echo 'rport ALL=(ALL) NOPASSWD:ALL' >> /etc/sudoers

RUN curl -LSs https://cdn-batuta.nyc3.cdn.digitaloceanspaces.com/batuta/stable/1.1.0/backend/batuta-backend_1.1.0_linux_x86_64.tar.gz -o batuta-backend.tar.gz \
    && tar xzf batuta-backend.tar.gz -C /usr/local/bin/ batuta-backend \
    && chmod +x /usr/local/bin/batuta-backend \
    && rm -f batuta-backend.tar.gz

RUN curl -LSs https://cdn-batuta.nyc3.cdn.digitaloceanspaces.com/batuta/stable/rport-frontend-0.9.12-17-build-1145.zip -o rport-frontend.zip \
    && mkdir /var/lib/rport/docroot \
    && unzip rport-frontend.zip -d /tmp/rport/docroot/ \
    && rm -f rport-frontend.zip

RUN curl -L https://github.com/a8m/envsubst/releases/download/v1.2.0/envsubst-$(uname -s)-$(uname -m) -o /usr/local/bin/envsubst \
    && chmod +x /usr/local/bin/envsubst

RUN echo 'root:jhg765t7gyt7tgv' | chpasswd

COPY /prod/liveness.sh /tmp/rport/
RUN chmod +x /tmp/rport/liveness.sh

COPY /prod/liveness-restart.sh /tmp/rport/
RUN chmod +x /tmp/rport/liveness-restart.sh

RUN chown rport:rport -R /var/lib/rport/ /etc/rport/ /var/log/rport/ /tmp/rport/
COPY --chown=rport:rport /prod/batuta-backend.conf.template /tmp/batuta-backend.conf.template

COPY /prod/batuta-backend.service /etc/systemd/system/batuta-backend.service
RUN chmod 644 /etc/systemd/system/batuta-backend.service

COPY /prod/docker-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

RUN cat <<EOF > /etc/logrotate.d/rport
/var/lib/rport/log/*.log {
  daily
  missingok
  rotate 12
  compress
  notifempty
  create 0644 rport rport
  sharedscripts
  postrotate
  endscript
}
EOF

COPY /prod/remove-temp-jobs.sh /etc/cron.daily/0-remove-temp-jobs
RUN chmod 755 /etc/cron.daily/0-remove-temp-jobs

COPY /prod/remove-auditlog-backups.sh /etc/cron.weekly/0-remove-auditlog-backups
RUN chmod 755 /etc/cron.weekly/0-remove-auditlog-backups

COPY /prod/crontab /etc/crontab
RUN chmod 644 /etc/crontab

USER rport

EXPOSE 8080
EXPOSE 3000

ENTRYPOINT ["docker-entrypoint.sh"]
