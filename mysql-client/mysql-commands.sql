CREATE DATABASE IF NOT EXISTS `MYSQL_CLIENT_DATABASE_NAME` DEFAULT CHARACTER SET=utf8;
CREATE USER IF NOT EXISTS 'MYSQL_CLIENT_USER' IDENTIFIED WITH mysql_native_password BY 'MYS<PERSON>_CLIENT_PASSWORD';
ALTER USER 'MYSQL_CLIENT_USER'@'%' IDENTIFIED WITH mysql_native_password BY 'MYSQL_CLIENT_PASSWORD';
GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, INDEX, DROP, ALTER, CREATE TEMPORARY TABLES, LOCK TABLES ON `MYSQL_CLIENT_DATABASE_NAME`.* TO 'MYSQL_CLIENT_USER'@'%';
USE `MYSQL_CLIENT_DATABASE_NAME`;
CREATE TABLE IF NOT EXISTS clients_auth (id varchar(100) PRIMARY KEY, password varchar(100) NOT NULL);
CREATE TABLE IF NOT EXISTS users (username varchar(150) NOT NULL PRIMARY KEY, password varchar(255) NOT NULL, password_expired bool NOT NULL default false, two_fa_send_to varchar(150), token varchar(128) default NULL, totp_secret longtext);
CREATE TABLE IF NOT EXISTS `groups` (username varchar(150) NOT NULL, `group` varchar(150) NOT NULL, PRIMARY KEY (username,`group`));
CREATE TABLE IF NOT EXISTS group_details (name varchar(150) NOT NULL PRIMARY KEY, permissions longtext DEFAULT ('{}'));
CREATE TABLE IF NOT EXISTS browser_login (url varchar(150) NOT NULL, username varchar(150) NOT NULL PRIMARY KEY, password varchar(100) NOT NULL);
INSERT IGNORE INTO `groups` VALUES('admin', 'Administrators');
INSERT IGNORE INTO `groups` VALUES('soar', 'Administrators');
INSERT IGNORE INTO browser_login VALUES('https://FQDN', 'admin', 'API_AUTH_PASSWORD');
INSERT IGNORE INTO users VALUES('admin', 'PASSWORD_HASH', 0, null, '', '');
INSERT IGNORE INTO users VALUES('soar', 'PASSWORD_HASH', 0, null, '', '');