#!/bin/bash
#set -ex

setup_db() {
    if [ "$DATABASE_TYPE" == "mysql" ]; then
        echo "Using MySQL as Database. Please, make sure to set it up first."
    else
        AUTH_DB_NAME="/var/lib/rport/auth.db"

        [ -n "${API_AUTH_PASSWORD}" ] || API_AUTH_PASSWORD=$(pwgen 9 1 2>/dev/null || openssl rand -hex 5)

        PASSWORD_HASH=$(htpasswd -nbB password "$API_AUTH_PASSWORD" | cut -d: -f2)

        sqlite3 $AUTH_DB_NAME 'PRAGMA journal_mode=WAL;' \
            'CREATE TABLE IF NOT EXISTS "users" ("username" TEXT(150) NOT NULL, "password" TEXT(255) NOT NULL, "password_expired" BOOLEAN NOT NULL CHECK (password_expired IN (0, 1)) DEFAULT 0, "token" TEXT(36) DEFAULT NULL, "two_fa_send_to" TEXT(150), "totp_secret" TEXT DEFAULT "");' \
            'CREATE UNIQUE INDEX IF NOT EXISTS "main"."username" ON "users" ("username" ASC);' \
            'CREATE TABLE IF NOT EXISTS "groups" ("username" TEXT(150) NOT NULL, "group" TEXT(150) NOT NULL);' \
            'CREATE UNIQUE INDEX IF NOT EXISTS "main"."username_group" ON "groups" ("username" ASC, "group" ASC);' \
            'CREATE TABLE IF NOT EXISTS "group_details" ("name" TEXT(150) NOT NULL, "permissions" TEXT DEFAULT "{}");' \
            'CREATE UNIQUE INDEX IF NOT EXISTS "main"."name" ON "group_details" ("name" ASC);' \
            "INSERT OR IGNORE INTO users VALUES('admin', '$PASSWORD_HASH', 0, null, '', '');" \
            "INSERT OR IGNORE INTO users VALUES('soar', '$PASSWORD_HASH', 0, null, '', '');" \
            "INSERT OR IGNORE INTO groups VALUES('admin', 'Administrators');" \
            "INSERT OR IGNORE INTO groups VALUES('soar', 'Administrators');" \
            'CREATE TABLE IF NOT EXISTS "clients_auth" ("id" varchar(100) PRIMARY KEY, "password" varchar(100) NOT NULL);' \
            'CREATE TABLE IF NOT EXISTS "browser_login" ("url" TEXT(150) NOT NULL, "username" TEXT(150) NOT NULL, "password" varchar(100) NOT NULL);' \
            'CREATE UNIQUE INDEX IF NOT EXISTS login_username ON browser_login ("username" ASC);' \
            "INSERT OR IGNORE INTO browser_login VALUES('https://$FQDN', 'admin', '$API_AUTH_PASSWORD');"

        # Print login info to the console
        echo ""
        sqlite3 $AUTH_DB_NAME ".mode column" "SELECT * FROM browser_login;"
        echo ""
    fi

    API_TOKEN_DB_NAME="/var/lib/rport/api_token.db"

    if [ ! -f "$API_TOKEN_DB_NAME" ]; then
        sqlite3 $API_TOKEN_DB_NAME 'PRAGMA journal_mode=WAL;' \
            "DROP TABLE IF EXISTS api_token;" \
            "CREATE TABLE api_tokens (username TEXT NOT NULL CHECK (username != ''), prefix TEXT NOT NULL CHECK (prefix != ''), created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP, expires_at DATETIME, scope TEXT, token TEXT NOT NULL, name TEXT NOT NULL, PRIMARY KEY (username, prefix)) WITHOUT ROWID;" \
            "CREATE UNIQUE INDEX api_tokens_unique_name ON api_tokens (username, name);" \
            "CREATE TABLE schema_migrations (version uint64,dirty bool);" \
            "INSERT INTO schema_migrations VALUES(3,'false');"
    fi

    TOKEN=$(htpasswd -nbB password "$RPORT_API_KEY" | cut -d: -f2)
    sqlite3 $API_TOKEN_DB_NAME "INSERT INTO api_tokens VALUES('soar', '$RPORT_API_PREFIX', CURRENT_TIMESTAMP, NULL, 'read+write', '$TOKEN', 'soar-api') ON CONFLICT DO UPDATE SET prefix = '$RPORT_API_PREFIX', token = '$TOKEN';"

    CLIENT_GROUPS_DB_NAME="/var/lib/rport/client_groups.db"

    if [ ! -f "$CLIENT_GROUPS_DB_NAME" ]; then
        sqlite3 $CLIENT_GROUPS_DB_NAME 'PRAGMA journal_mode=WAL;' \
            "CREATE TABLE IF NOT EXISTS client_groups (id TEXT PRIMARY KEY NOT NULL, description TEXT NOT NULL, params TEXT NOT NULL, allowed_user_groups TEXT NOT NULL DEFAULT '[]') WITHOUT ROWID;" \
            "CREATE TABLE IF NOT EXISTS schema_migrations (version uint64,dirty bool);" \
            "INSERT INTO schema_migrations VALUES(2,'false');"
    fi

    sqlite3 $CLIENT_GROUPS_DB_NAME "INSERT OR IGNORE INTO client_groups VALUES('online','Online hosts','{\"client_id\":null,\"name\":null,\"os\":null,\"os_arch\":null,\"os_family\":null,\"os_kernel\":null,\"hostname\":null,\"ipv4\":null,\"ipv6\":null,\"tag\":null,\"version\":null,\"address\":null,\"client_auth_id\":null,\"connection_state\":[\"connected\"]}','[\"Administrators\"]');"
}

main() {
    setup_db

    envsubst < /tmp/batuta-backend.conf.template > /etc/rport/batuta-backend.conf

    sudo systemctl enable cron
    sudo systemctl start cron

    sudo systemctl daemon-reload
    sudo systemctl enable batuta-backend
    sudo systemctl start batuta-backend

    tail -f /var/lib/rport/log/batuta-backend.log
}

cp /tmp/rport/liveness.sh /var/lib/rport/
chmod 775 /var/lib/rport/liveness.sh

cp /tmp/rport/liveness-restart.sh /var/lib/rport/
chmod 775 /var/lib/rport/liveness-restart.sh

rm -Rf /var/lib/rport/docroot
cp -R /tmp/rport/docroot /var/lib/rport/
mkdir -p /var/lib/rport/log
echo -n "" > /var/lib/rport/log/batuta-backend.log

main
