name: Build Image and Push to ECR
on:
  workflow_call:
    secrets:
      AWS_ACCESS_KEY_ID:
        required: true
      AWS_SECRET_ACCESS_KEY:
        required: true
    inputs:
      ECR_REPOSITORY:
        required: true
        type: string
      AWS_REGION:
        required: true
        type: string
      VERSION:
        required: true
        type: string
      AWS_ACCOUNT_ID_PROD:
        required: true
        type: string
    outputs:
      tag:
        description: "Tag of the new built docker image"
        value: ${{ jobs.build.outputs.tag }}

jobs:
  build:
    name: Build Image and Push to ECR
    permissions:
      id-token: write
      contents: write
    env:
      ECR_REPOSITORY: ${{ inputs.ECR_REPOSITORY }}
      IMAGE_TAG: ${{ github.sha }}
      VERSION: ${{ inputs.VERSION }}
    runs-on: ubuntu-22.04
    outputs:
      tag: ${{ steps.set_github_run_number.outputs.outtag }}
    steps:
      - name: Checkout Git Code
        uses: actions/checkout@v3

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{  secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Cache Docker layers
        uses: actions/cache@v3
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{  secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Get ECR login information
        id: get-ecr-password
        run: echo "::set-output name=password::$(aws ecr get-login-password)"

      - name: Set GITHUB_RUN_NUMBER
        id: set_github_run_number
        run: |
          echo GITHUB_RUN_NUMBER=$(( GITHUB_RUN_NUMBER + 279 ))-${{env.VERSION}} >> $GITHUB_ENV
          echo "outtag=${{ github.event.repository.name }}-$((GITHUB_RUN_NUMBER + 279))-${{env.VERSION}}" >> $GITHUB_OUTPUT

      - name: Build and push
        uses: docker/build-push-action@v1
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        with:
          dockerfile: "${{env.VERSION}}/Dockerfile"
          registry: ${{ steps.login-ecr.outputs.registry }}
          repository: ${{env.ECR_REPOSITORY}}
          username: AWS
          password: ${{ steps.get-ecr-password.outputs.password }}
          add_git_labels: true
          tag_with_ref: true
          tags: "${{ github.event.repository.name }}-${{env.GITHUB_RUN_NUMBER}},${{env.VERSION}}"
          push: true
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache-new,mode=max

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: "${{inputs.AWS_ACCOUNT_ID_PROD}}.dkr.ecr.us-east-1.amazonaws.com/${{env.ECR_REPOSITORY}}:${{ github.event.repository.name }}-${{env.GITHUB_RUN_NUMBER}}"
          format: "table"
        env:
          TRIVY_DB_REPOSITORY: public.ecr.aws/aquasecurity/trivy-db
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{  secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_DEFAULT_REGION: us-east-1
