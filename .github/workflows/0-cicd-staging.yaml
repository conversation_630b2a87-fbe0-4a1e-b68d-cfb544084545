name: STAGING Batuta Backend CICD - Build Push Docker Deploy ArgoCD - Backend STAGING
on:
  workflow_dispatch:
  push:
    branches:
      - staging
    paths:
    - 'stg/**'
    - '.github/workflows/0-cicd-stg-staging.yaml'
  pull_request:
    branches:
      - "staging"
    paths:
    - 'stg/**'
    - '.github/workflows/0-cicd-stg-staging.yaml'

jobs:
  call-ci-workflow-staging:
    uses: ./.github/workflows/1-build-image-staging.yaml
    secrets:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID_STAGING }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY_STAGING }}
    with:
      ECR_REPOSITORY: batuta-staging-us-east-1-ecr-backend
      AWS_REGION: us-east-1
      BACKEND_VERSION: stg
      AWS_ACCOUNT_ID_STAGING: ${{vars.AWS_ACCOUNT_ID_STAGING }}

  tag-docker-image-staging:
    if: github.ref == 'refs/heads/staging'
    uses: ./.github/workflows/2-cd-update-tag-staging.yaml
    needs: call-ci-workflow-staging
    with:
      environment: "staging"
      service: batuta-backend
      tag: ${{needs.call-ci-workflow-staging.outputs.tag}}
      values-path: application/staging/clients
      tag-path: "image.tag"
    secrets:
      token: ${{ secrets.GH_TOKEN }}
