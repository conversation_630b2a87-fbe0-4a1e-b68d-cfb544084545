name: PROD Batuta Backend CICD - Build Push Docker Deploy ArgoCD - Backend DEBUG PROD
on:
  push:
    branches:
      - main
    paths:
    - 'debug-prod/**'
    - '.github/workflows/0-cicd-debug-prod.yaml'
  pull_request:
    branches:
      - "main"
    paths:
    - 'debug-prod/**'
    - '.github/workflows/0-cicd-debug-prod.yaml'

jobs:
  call-ci-workflow-prod:
    uses: ./.github/workflows/1-build-image-prod.yaml
    secrets:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID_PROD }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY_PROD }}
    with:
      ECR_REPOSITORY: batuta-prod-us-east-1-ecr-backend
      AWS_REGION: us-east-1
      BACKEND_VERSION: debug-prod
      AWS_ACCOUNT_ID_PROD: ${{vars.AWS_ACCOUNT_ID_PROD }}
