name: PROD Batuta Backend CICD - Build Push Docker Deploy ArgoCD - MySQL
on:
  push:
    branches:
      - main
    paths:
      - "mysql-client/**"
      - ".github/workflows/0-cicd-mysql-client-prod.yaml"
  pull_request:
    branches:
      - main
    paths:
      - "mysql-client/**"
      - ".github/workflows/0-cicd-mysql-client-prod.yaml"

jobs:
  call-ci-workflow-prod:
    uses: ./.github/workflows/1-build-image-mysql-client-prod.yaml
    secrets:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID_prod }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY_prod }}
    with:
      ECR_REPOSITORY: batuta-prod-us-east-1-ecr-backend
      AWS_REGION: us-east-1
      VERSION: mysql-client
      AWS_ACCOUNT_ID_prod: ${{vars.AWS_ACCOUNT_ID_prod }}
