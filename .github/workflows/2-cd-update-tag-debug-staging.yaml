name: Update Tags
on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
        description: "Environment that will be updated with new image tag"
      service:
        required: true
        type: string
        description: "Service that will be updated with new image tag"
      tag:
        required: true
        type: string
        description: "Image tag"
      values-path:
        required: true
        type: string
      tag-path:
        required: false
        type: string
        default: "image.tag"
      repository:
        required: false
        type: string
        default: "Humanapis/batuta-deployments"
    secrets:
      token:
        description: 'A token passed from the caller workflow'
        required: false

jobs:
  promote-to-env:
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}
    steps:
      # Clone Repository
    - name: Checkout private tools
      uses: actions/checkout@v3
      with:
        repository: ${{ inputs.repository }}
        token: ${{ secrets.token }}
        ref: main

    - name: Install yq
      id: setup-yq
      uses: shiipou/setup-yq-action@v2.2.0

    - name: Update Tags
      env:
        CLIENTS: ${{ vars.CLIENTS }}
      run: |
        # change tag with yq
        git config --global user.email "github-actions[bot]@users.noreply.github.com"
        git config --global user.name "github-actions[bot]"

        for client in $CLIENTS; do
          echo "Updating $client ..."
          yq -i ".${{inputs.service}}.${{inputs.tag-path}} = \"${{inputs.tag}}\"" \
           "${{ inputs.values-path }}/client-$client/batuta-backend/values.yaml"
        done

        git add .
        git commit -m "AUTO: Bump ${{ inputs.service }} DEBUG STAGING image tag to ${{ inputs.tag }} for env ${{ inputs.environment }}"
        git push
