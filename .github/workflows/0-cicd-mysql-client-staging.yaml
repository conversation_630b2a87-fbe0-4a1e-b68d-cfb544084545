name: STAGING Batuta Backend CICD - Build Push Docker Deploy ArgoCD - MySQL
on:
  push:
    branches:
      - staging
    paths:
      - "mysql-client/**"
      - ".github/workflows/0-cicd-mysql-client-staging.yaml"
  pull_request:
    branches:
      - staging
    paths:
      - "mysql-client/**"
      - ".github/workflows/0-cicd-mysql-client-staging.yaml"

jobs:
  call-ci-workflow-staging:
    uses: ./.github/workflows/1-build-image-mysql-client-staging.yaml
    secrets:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID_STAGING }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY_STAGING }}
    with:
      ECR_REPOSITORY: batuta-staging-us-east-1-ecr-backend
      AWS_REGION: us-east-1
      VERSION: mysql-client
      AWS_ACCOUNT_ID_STAGING: ${{vars.AWS_ACCOUNT_ID_STAGING }}
