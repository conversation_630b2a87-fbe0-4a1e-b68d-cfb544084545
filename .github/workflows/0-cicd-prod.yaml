name: PROD Batuta Backend CICD - Build Push Docker Deploy ArgoCD - Backend PROD
on:
  push:
    branches:
      - main
    paths:
    - 'prod/**'
    - '.github/workflows/0-cicd-prod.yaml'
  pull_request:
    branches:
      - "main"
    paths:
    - 'prod/**'
    - '.github/workflows/0-cicd-prod.yaml'

jobs:
  call-ci-workflow-prod:
    uses: ./.github/workflows/1-build-image-prod.yaml
    secrets:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID_PROD }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY_PROD }}
    with:
      ECR_REPOSITORY: batuta-prod-us-east-1-ecr-backend
      AWS_REGION: us-east-1
      BACKEND_VERSION: prod
      AWS_ACCOUNT_ID_PROD: ${{vars.AWS_ACCOUNT_ID_PROD }}

  tag-docker-image-prod:
    if: github.ref == 'refs/heads/main'
    uses: ./.github/workflows/2-cd-update-tag-prod.yaml
    needs: call-ci-workflow-prod
    with:
      environment: "prod"
      service: batuta-backend
      tag: ${{needs.call-ci-workflow-prod.outputs.tag}}
      values-path: application/prod/clients
      tag-path: "image.tag"
    secrets:
      token: ${{ secrets.GH_TOKEN }}
