#======================================================================================================================
# vim: softtabstop=2 shiftwidth=2 expandtab fenc=utf-8 spell spelllang=en cc=120
#======================================================================================================================
#
#          FILE: batuta-backend.example.config
#
#   DESCRIPTION: Configuration file for the rport server
#
#          BUGS: https://github.com/cloudradar-monitoring/rport/issues
#
#          HELP: https://github.com/cloudradar-monitoring/rport/blob/master/README.md
#
#     COPYRIGHT: (c) 2020 by the CloudRadar Team,
#
#       LICENSE: MIT
#  ORGANIZATION: RealVNC Limited, Cambridge, United Kingdom (realvnc.com)
#       CREATED: 10/10/2020
#       UPDATED: 27/10/2022
#======================================================================================================================

[server]
  ## Defines the IP address and port the HTTP server listens on.
  ## This is where the rport clients connect to.
  ## Defaults: "0.0.0.0:8080"
  address = "${SERVER_ADDRESS:-0.0.0.0:8080}"

  ## Optionally defines full client connect URL(s).
  ## Defaults to http://{address}
  ## This setting is only used to return via an API call where batuta-backend is listening for client connections.
  ## The pairing script generates a ready-to-use client configuration based on this {url}.
  ## Also useful, if you run the batuta-backend behind a reverse proxy or a port forwarding
  ## and the external URL differs from the internal address and port.
  ## Accepts a single string like 'url = "http://rport.example.com"'
  ## or a list like 'url = ["http://rport.example.com", "http://***********:8000"]
  url = ["http://${FQDN_HTTP}:80"]

  ## Optionally defines the hostname or IP address used to generate links pointing to running tunnels.
  ## By default, all links are relative to the URL of the API or UI.
  ## If you run the API/UI behind a reverse proxy that is incapable of forwarding raw TCP/UDP packets,
  ## you can specify a separated tunnel_host to access tunnels, bypassing the reverse proxy.
  #tunnel_host = "tunnels.rport.example.com"

  ## Optionally defines a custom pairing service URL.
  ## Defaults to https://pairing.rport.io
  ## To run your own pairing service, refer to https://github.com/cloudradar-monitoring/rport-pairing
  ## Accepts a single string like
  #pairing_url = "https://pairing.example.com"

  ## An optional string to seed the generation of a ECDSA public and private key pair.
  ## Highly recommended. Not using it is a big security risk.
  ## Use "openssl rand -hex 18" to generate a secure key seed.
  key_seed = "${SERVER_KEY_SEED}"

  ## An optional string representing a single client auth credentials, in the form of <client-auth-id>:<password>.
  ## This is equivalent to creating an {auth_file} with '{"<client-auth-id>":"<password>"}'.
  ## Use either {auth_file}/{auth_table} or {auth}. Not both.
  ## If multiple auth options are enabled, batuta-backend exits with an error.
  #auth = "clientAuth1:1234"

  ## An optional path to a json file with client credentials.
  ## This is for authentication of the rport tunnel clients.
  ## The file should contain a map with clients credentials defined like:
  ## {
  ##   "<client-auth-id1>": "<password1>",
  ##   "<client-auth-id2>": "<password2>"
  ## }
  ## Use either {auth_file}/{auth_table} or {auth}. Not both.
  ## If multiple auth options are enabled, batuta-backend exits with an error.
  #auth_file = "/var/lib/rport/client-auth.json"

  ## An optional name of a database table for client authentication.
  ## Requires a global database connection. See below.
  ## The table must be created manually.
  ## Learn how to create them https://docs.rport.io/get-started/client-authentication/#using-a-database-table
  auth_table = "clients_auth"

  ## When using an {auth_file} or a database table, creating separate credentials for each client is recommended.
  ## It increases security because you can lock out clients individually.
  ## If {auth_multiuse_creds} is false, a client is rejected if another client with the same id is connected
  ## or has been connected within the {keep_disconnected_clients} interval.
  ## Defaults: true
  auth_multiuse_creds = false

  ## Having set {auth_multiuse_creds} = false, you can omit specifying a client-id.
  ## You can use the client-auth-id as client-id to slim down the client configuration.
  ## Defaults: false
  equate_clientauthid_clientid = true

  ## If you want to delegate the creation and maintenance to an external tool
  ## you should turn {auth_write} off.
  ## The API will reject all writing access to the client auth with HTTP 403.
  ## Applies only to {auth_file} and {auth_table}.
  ## Default: true
  #auth_write = true

  ## Specifies another HTTP server to proxy requests to when batuta-backend receives a normal HTTP request.
  #proxy = "http://intranet.lan:8080/"

  ## Defines a list of port numbers or ranges of server ports,
  ## that would be used for automatic and manual port assignment.
  ## Creating reverse tunnels will fail if the requested server port is not listed here.
  ## Defaults to ['20000-30000'].
  ## Example:
  ## used_ports = [ '1024-2000', '9000', '9090' ]
  # used_ports = ['20000-30000']

  ## Defines a list of port numbers or ranges of server ports,
  ## that would not be used for automatic and manual port assignment.
  ## Values that are not included in the {used_ports} are ignored.
  ## Defaults to ['1-1024'].
  ## If no ports should be excluded, then set it to "[]".
  #excluded_ports = ['1-1024']

  ## An optional param to define a local directory path to store internal data.
  ## By default, "/var/lib/rport" is used.
  ## If the directory doesn't exist, it will be created.
  ## On Linux, you must create this directory because an unprivileged user
  ## don't have the right to create a directory in /var/lib.
  ## Ideally, this directory is the homedir of the rport user and has been created along with the user.
  ## Example: useradd -r -d /var/lib/rport -m -s /bin/false -U -c "System user for rport client and server" rport
  data_dir = "${SERVER_DATA_DIR:-/var/lib/rport}"

  ## By default all Sqlite3 databases are opened with WAL enabled.
  ## This is a performance enhancement. Do not turn off, unless you have good reasons.
  #sqlite_wal = true

  ## Limits the concurrent SSH handshakes to prevent resource strain, especially during mass reconnections after downtimes.
  ## A low value can lead to client timeouts and extended reconnect durations due to exponential backoff.
  ## However, setting a value too high might overburden the server's CPU and network bandwidth during peak connection periods.
  ## Adjust based on server resources, anticipated client load, and post-downtime performance.
  ## For comprehensive insights and guidance, refer to https://docs.rport.io/advanced/fine-tuning-the-rport-server/.
  ## Default is 16.
  max_concurrent_ssh_handshakes = ${SERVER_MAX_HANDSHAKES:-16}

  ## Set a timeout to the accept SSH connection process, avoiding the block of the concurrent handshakes.
  ## Value can contain suffixes "h"(hours), "m"(minutes), "s"(seconds).
  ## Default is 30s.
  accept_SSH_connection_timeout = "${SERVER_SSH_TIMEOUT:-30s}"

  ## An optional parameter to define whether disconnected clients get purged from the database.
  ## By default, disconnected clients are NOT purged.
  purge_disconnected_clients = ${SERVER_PURGE_DISCONNECTED_CLIENTS:-false}

  ## An optional parameter to define a duration to keep info (clients, tunnels, etc) about disconnected clients.
  ## By default is "1h". A value of "0" means disconnected clients are purged immediately.
  ## Requires 'purge_disconnected_clients = true', otherwise ignored.
  ## Value can contain suffixes "h"(hours), "m"(minutes), "s"(seconds); Maximum allowed: 168h (=7days)
  #keep_disconnected_clients = "1h"

  ## An optional parameter to define an interval to purge disconnected clients from storage.
  ## Requires 'purge_disconnected_clients = true', otherwise ignored
  ## Value can contain suffixes "h"(hours), "m"(minutes), "s"(seconds).
  ## By default, 1 minute is used.
  #purge_disconnected_clients_interval = "1m"

  ## A background task will continuously check the client connection status by sending pings at the specified interval.
  ## Value can contain suffixes "h"(hours), "m"(minutes), "s"(seconds).
  ## Enabled by default with a '5m' interval. This task cannot be switched off. Fastest interval allowed = '2m'
  #check_clients_connection_interval = "5m"

  ## Timeout per client for the above clients' connection check.
  ## If client does not respond within timeout, it's considered disconnected.
  ## Value can contain suffixes "h"(hours), "m"(minutes), "s"(seconds).
  ## By default, 30 seconds are used.
  #check_clients_connection_timeout = "30s"

  ## An optional parameter to define a limit for data that can be sent by rport clients.
  ## By default is set to 524288(512Kb).
  #max_request_bytes_client = 524288

  ## An optional parameter to define a timeout in seconds to observe the remote command execution.
  ## Defaults: 60.
  #run_remote_cmd_timeout_sec = 60

  ## An optional parameter to define a timeout to check whether a remote destination of a requested new tunnel is available,
  ## i.e. whether a given remote port is open on a client machine. By default, "2s" is used.
  #check_port_timeout = "1s"

  ## There is no technical requirement to run the rport server under the root user.
  ## Running it as root is an unnecessary security risk.
  ## You don't even need root-rights to run rport on tcp ports below 1024.
  ## Use "setcap CAP_NET_BIND_SERVICE=+eip /usr/local/bin/batuta-backend" to allow the usage of any tcp ports.
  ## Hint: setcap must be applied after each update of the binary.
  ##       Consider integrating it into the systemd service file as an ExecStartPre option.
  ## Rport exits with an error if started as root, unless you explicitly allow it.
  ## Defaults to false
  #allow_root = false

  ## Protect your server against password guessing.
  ## Force clients to wait N seconds (float) between unsuccessful login attempts.
  ## This is per client auth id.
  ## A message like
  ##    'client-listener: Failed login attempt for client auth id "abc", forcing to wait for {client_login_wait}s ({ip})'
  ## is logged to the info log.
  ## Consider changing the log_level to 'info' to trace failed login attempts.
  ## Learn more https://docs.rport.io/advanced/securing-the-rport-server/
  ## Defaults: 2.0
  #client_login_wait = 2.0

  ## After {max_failed_login} consecutive failed login-in attempts, ban the source IP address for {ban_time} seconds.
  ## HTTP Status 423 is returned.
  ## A message like
  ##     'Maximum of {max_failed_login} login attempts reached. Visitor ({remote-ip}) banned. Ban expiry: 2021-04-16T11:22:26+00:00'
  ## is logged to the info log.
  ## Banning happens on HTTP level.
  ## Consider banning on network level using fail2ban.
  ## Learn more https://docs.rport.io/advanced/securing-the-rport-server/
  ## Defaults: max_failed_login = 5, ban_time = 3600
  #max_failed_login = 5
  #ban_time = 3600

  ## Enable the creation of tunnel proxies with giving certificate- and key-file
  ## Defaults: not enabled
  #tunnel_proxy_cert_file = "/var/lib/rport/server.crt"
  #tunnel_proxy_key_file = "/var/lib/rport/server.key"

  ## The built-in ACME can be used to generate and manage Let's encrypt certificates for tunnel.
  ## Requires `tunnel_host` to be set. See https://docs.rport.io/get-started/securing-rportd-with-https/#use-the-built-in-acme
  #tunnel_enable_acme = false

  ## Allowed origins for tunnel cross-origin requests.
  #tunnel_cors = []

  ## If specified, batuta-backend will serve novnc javascript app from this directory.
  #novnc_root = "/var/lib/rport/novncroot"

  ## Host and port where guacd daemon is listening.
  ## If specified, batuta-backend will serve remote desktop connections in browser through Apache Guacamole.
  #guacd_address = "127.0.0.1:4822"

  ## Maximum number of results to keep for commands, scripts and schedules execution
  jobs_max_results = ${JOBS_MAX_RESULTS:-200000}

  ## Minimal TLS version required for Internal Tunnel
  ## Default 1.3
  ## Possible settings: 1.3 or 1.2
  #tls_min = "1.3"

  ## If the built-in ACME is used, the API port must be constantly available on port 443 from the Internet.
  ## Alternatively you can use HTTP-01 ACME challenge by making the below port constantly available
  ## on port 80 from the Internet. See https://docs.rport.io/get-started/securing-rportd-with-https/#use-the-built-in-acme
  #acme_http_port = 80

[logging]
  ## Specifies log file path for global logging
  ## Not setting {log_file} turns logging off.
  log_file = "/var/lib/rport/log/batuta-backend.log"
  #log_file = "/dev/stdout"
  ## Specify log level. Values: 'error', 'info', 'debug'.
  ## Defaults to 'info'
  log_level = "info"

[api]
  ## Defines the IP address and port the API server listens on.
  ## Specify non-empty {address} to enable API support.
  address = "${API_ADDRESS:-0.0.0.0:3000}"

  ## Optionally define the base URL used to access the rport API and UI.
  ## This is how you access the API from the outside.
  ## The hostname of the URL must have a publicly available DNS record.
  #base_url = "https://rport.example.com:443"

  ## The built-in ACME can be used to generate and manage Let's encrypt certificates.
  ## The base_url above must be set. See https://docs.rport.io/get-started/securing-rportd-with-https/#use-the-built-in-acme
  #enable_acme = false

  ## Defines <user>:<password> authentication pair for accessing the API. Enables access for a single user.
  ## Learn more about api auth options and get examples https://docs.rport.io/get-started/api-authentication/
  ## Use either {auth_file}/{auth_user_table} or {auth}. Not both.
  ## If multiple auth options are enabled, batuta-backend exits with an error.
  #auth = "admin:foobaz"

  ## Use a json file containing users, passwords and groups.
  ## Passwords are expected to be bcrypt encoded.
  ## Learn more about api auth options and get examples https://docs.rport.io/get-started/api-authentication/
  ## Use either {auth_file}/{auth_user_table} or {auth}. Not both.
  ## If multiple auth options are enabled, batuta-backend exits with an error.
  #auth_file = "/var/lib/rport/api-auth.json"

  ## An optional name of database tables for api user authentication.
  ## Requires a global database connection. See below.
  ## Tables must be created manually.
  ## Learn how to create them https://docs.rport.io/get-started/api-authentication/#database
  auth_user_table = "users"
  auth_group_table = "groups"
  auth_group_details_table = "group_details"

  ## The rport server can treat all requests as pre-authenticated by a reverse proxy based on a http header.
  ## This option is enabled if auth_header is set.
  ## If the header exists, the request is considered valid and a session is created.
  ## Inside the same or a different header, the username must be submitted.
  #auth_header = "Authentication-IsAuthenticated"
  #user_header = "Authentication-User"
  ## If the user doesn't exist yet, it can be created on-the-fly.
  ## Disabled by default
  #create_missing_users = true
  ## If users are created on-the-fly to which user group do they belong?
  ## Applies also to all users created by an OAuth authentication (Rport Plus only)
  #default_user_group = "Administrators"

  ## Use two-factor authentication to generate auth tokens.
  ## Learn more about two-factor and how to send the tokens https://docs.rport.io/get-started/2fa-messaging/
  ## Using 2FA will disable HTTP basic authentication on all API endpoints except '/login'. It triggers sending 2FA
  ## verification code to a user using a chosen delivery method. This code can be further verified using '/verify-2fa' endpoint.
  ## Your user-password store (json files or DB table) needs an additional field 'two_fa_send_to'.
  ## 2FA is not available if you use a single static user-password pair set directly in the batuta-backend.conf.
  ## Supported values:
  ## 'smtp' requires smtp settings below
  ## 'pushover' requires pushover settings below
  ## '<URL>' e.g. "https://free-2fa-sender.rport.io" to send via free 2FA service, requires base_url to be set
  ## '<PATH>' e.g. "/usr/local/bin/2fa-sender.sh" to send via a script
  ## Executables must read recipients details from the environment. Check our examples from the link above.
  ## 2FA is disabled by default.
  #two_fa_token_delivery = 'smtp'
  ## Token sent via the specified delivery method has a default lifetime of 600 seconds.
  #two_fa_token_ttl_seconds = 600
  ## Sending the token has a default timeout of 10 seconds.
  #two_fa_send_timeout = 10s
  ## When using an executable for token delivery, you can optionally specify how the two_fa_send_to is validated on changes.
  ## Ignored when using other delivery methods.
  ## Use two_fa_send_to_type = 'email' to accept only valid email address.
  ## Or use a regular expression, for example
  ## two_fa_send_to_type = 'regex'
  ## two_fa_send_to_regex = '[a-z0-9]{10}'
  #two_fa_send_to_type = 'none'

  ## To enable time-based onetime tokens generated by apps likes Google or Microsoft Authenticator,
  ## set 'totp_enabled = true'.
  ## Your user-password store (json files or DB table) needs an additional text field 'totp_secret'.
  #totp_enabled = false
  ## Learn more on https://docs.rport.io/get-started/api-authentication/#two-factor-auth
  ## Before sending the token generated by the authenticator app,
  ## users should do a login attempt. Otherwise, they can request tokens directly without login.
  ## 'totp_login_session_ttl' sets the timeout after which totp codes won't be accepted
  #totp_login_session_ttl = '600s'

  ## If you run multiple RPort servers, you should give them different totp account names
  ## to differentiate them on your authenticator app.
  #totp_account_name = 'RPort'

  ## Defines JWT secret used to generate new tokens.
  ## If not set, it will be generated by server. (This causes all users to be logged out on server restart)
  ## Use 'pwgen 18 1' or 'openssl rand -hex 9' to generate a secure secret.
  jwt_secret = "${API_JWT_SECRET}"

  ## Sets the max JWT token lifetime in hours (total max allowable is 2160 hrs)
  # max_token_lifetime = 720

  ## If specified, batuta-backend will serve static files from this directory on the same API address.
  ## This is the place where the frontend files (html/js) go.
  ## Learn how to install the frontend https://docs.rport.io/get-started/rport-frontend/
  doc_root = "/var/lib/rport/docroot"

  ## If both cert_file and key_file are specified, then batuta-backend will use them to serve the API with https.
  ## Intermediate certificates should be included in cert_file if required.
  #cert_file = "/var/lib/rport/server.crt"
  #key_file = "/var/lib/rport/server.key"

  ## Specifies file for API access logs. Logs will be written in Combined Log Format.
  ## If this is not set, the API access logs are disabled.
  #access_log_file = "/var/log/rport/api-access.log"

  ## Protect your API server against password guessing.
  ## Force users to wait N seconds (float) between unsuccessful login attempts.
  ## This is per username.
  ## Defaults: 2.0
  #user_login_wait = 2.0

  ## After X failed login-in attempts, ban the source IP address for Z seconds.
  #max_failed_login = 5
  #ban_time = 3600

  ## Each action is logged and stored in a database to follow up who did what when.
  ## The audit log is enabled by default. The data is stored in {data_dir}.audit_log.db
  enable_audit_log = true

  ## Storing the full remote IP address of the API users might violate privacy protection regulations.
  ## Using obfuscation stores only the first three segments of the IP address (IPv4 only).
  ## Turned off by default.
  #use_ip_obfuscation = false

  ## Depending on the number of actions your rport server performs, the audit log can grow fast.
  ## By default, the audit_log database file is changed every month.
  ## Consider changing to a faster rotation.
  #audit_log_rotation = 'monthly', possible values: yearly, monthly, weekly, daily
  audit_log_rotation = 'weekly'

  ## Required minimal password length
  ## Default: 14
  #password_min_length = 14

  ## Perform the so-called zxcvbn check to reject passwords based on common words.
  ## Enabled by default. The zxcvbn check is executed locally. No API calls.
  ## Default 0 (minimum risky password: "too guessable")
  ## 1 modest protection from throttled online attacks: "very guessable"
  ## 2 modest protection from unthrottled online attacks: "somewhat guessable"
  ## 3 modest protection from offline attacks: "safely unguessable"
  ## 4 strong protection from offline attacks under same scenario: "very unguessable"
  ## -1 zxcvbn check is disabled
  #password_zxcvbn_minscore = 0

  ## Minimal TLS version required for the API
  ## Default 1.3
  ## Possible settings: 1.3 or 1.2
  #tls_min = "1.3"

  ## An optional parameter to define a limit for data that can be sent by API requests.
  ## By default is set to 10240(10Kb).
  max_request_bytes = ${API_MAX_REQUEST_BYTES:-153600}

  ## The maximum upload size of a file in bytes.
  ## If exceeded, an error is returned. Please note that max_request_bytes is not affecting the file upload API
  ## https://docs.rport.io/advanced/file-reception/
  ## Defaults: 10485760 bytes (~ 10.5 MB).
  #max_filepush_size = 10485760

  ## Allowed origins for cross-origin requests.
  #cors = []

  ## To enable testing endpoints (/test/commands/ui and /test/scripts/ui) for ws endpoints (/ws/commands and /ws/scripts) provide
  ## true for `enable_ws_test_endpoints`
  ## Defaults: enable_ws_test_endpoints = false
  #enable_ws_test_endpoints = false

[database]
  ## Global configuration of a database connection.
  ## The database and the initial schema must be created manually.
  ## Learn how to use a database:
  ##  for api auth: https://docs.rport.io/get-started/api-authentication/#database
  ##  for clients auth:  https://docs.rport.io/get-started/client-authentication/#using-a-database-table
  ## Supported: MySQL/MariaDB and Sqlite3

  ## "mysql" For MySQL/MariaDB. "sqlite" For Sqlite3.
  db_type = "${DATABASE_TYPE:-sqlite}"

  ## Only for MySQL/MariaDB, ignored for Sqlite.
  db_host = "$MYSQL_DATABASE_HOST:$MYSQL_DATABASE_PORT"

  ## Credentials, only for MySQL/MariaDB, ignored for Sqlite.
  db_user = "$MYSQL_CLIENT_USER"
  db_password = "$MYSQL_CLIENT_PASSWORD"

  ## For MySQL/MariaDB name of the database. For Sqlite full path to the sqlite3 file (default value).
  db_name = "${DATABASE_NAME:-/var/lib/rport/auth.db}"

[caddy-integration]
  ## Enable https tunnels on random subdomains.
  ## See https://docs.rport.io/advanced/tunnels-on-subdomains/
  ## Note: no defaults currently.

  ## Specifies the path to the caddy executable. mandatory.
  #caddy="/usr/bin/caddy"
  ## The bind address where caddy should listen for subdomain tunnels connections. mandatory, including the port.
  # address="0.0.0.0:8443"
  ## All caddy subdomain tunnels will have the domain prefix listed below. mandatory.
  #subdomain_prefix="tunnels.rport.test"
  ## An SSL wildcard certificate is required that matches the subdomain prefix above. mandatory.
  #cert_file="/etc/letsencrypt/live/<YOUR-DOMAIN>/fullchain.pem"
  #key_file="/etc/letsencrypt/live/<YOUR-DOMAIN>/privkey.pem"

  ## If you want to run the API and the tunnel subdomains on the same HTTPs port,
  ## you must specify a hostname for the API.
  #api_hostname = "rport-api.example.com"
  ## Even if the above api_hostname is  inside the validity of the above certificate,
  ## you must specify wich certificate to use for the API.
  #api_cert_file = "/etc/letsencrypt/live/<YOUR-DOMAIN>/fullchain.pem"
  #api_key_file = "/etc/letsencrypt/live/<YOUR-DOMAIN>/privkey.pem"
  ## Port of the API with TLS switched off. Port must match the port of "[api] address"
  #api_port = "3000"

[pushover]
  ## Pushover settings for sending push messages via Pushover.net. Currently, used only for sending two-factor auth tokens.
  ## Learn more on https://docs.rport.io/get-started/2fa-messaging/#pushover
  ## Required (only if pushover is specified as {api.two_fa_token_delivery}): pushover API token and user key
  #api_token = 'abc123'
  #user_key = 'user123'

[smtp]
  ## SMTP settings for sending email. Currently, used only for sending two-factor auth tokens.
  ## Learn more on https://docs.rport.io/get-started/2fa-messaging/#smtp
  ## Required (only if smtp is specified as {api.two_fa_token_delivery}):
  ## smtp server and port separated by a colon. e.g. server = 'smtp.gmail.com:2525'
  ## sender email - an email that is used to send 2FA tokens
  ## Optional:
  ## auth_username, specify a username for authentication
  ## auth_password, specify a password for the username.
  ## secure = true|false, enable if Implicit(Forced) TLS must be used.
  #server = 'smtp.example.com:2525'
  #sender_email = '<EMAIL>'
  #auth_username = 'john.doe'
  #auth_password = 'secret'
  #secure = false

[notifications]
  ## RPort comes with the ability to send notifications via custom scripts. (Requires the plus plugin)
  ## Scripts are execute from the below directoy. Files must have the executable bit set.
  # notification_script_dir = "/usr/local/lib/rport/notification_scripts"
  ## The status of script executions and notification sent via buil-in SMTP client is logged.
  ## Logs are removed automatically after a given period, Use suffix d (=days) or h (=hours)
  ## Default: "7d"
  #log_storage_duration = "7d"

  ## interval in which checks and deletions of the outdated logs will happen
  #cleanup_interval = "1d"

[monitoring]
  ## https://docs.rport.io/advanced/monitoring/
  ## Global switch to turn off monitoing system wide. Any monitoring settings on
  ## client side will be ignored. Switched on by default.
  enabled = ${MONITORING_ENABLED:-true}
  ## The rport server stores monitoring data for a period of N.
  ## Use suffix d (=days) or h (=hours)
  ## Older data is purged automatically. Requires monitoring turned on.
  ## Make sure to incldue quotes "" around the value
  ## Default: "7d"
  data_storage_duration = "${MONITORING_DATA_STORAGE_DURATION:-1h}"

[plus-plugin]
  ## Rport Plus is a paid for binary extension to Rport. Learn more at https://plus.rport.io/
  # plugin_path = "/usr/local/lib/rport/rport-plus.so"

[plus-license]
  # id = "<your-license-id>"
  # key = "<your-license-key>"
  ## An optional web proxy to be used for outbound connections
  # proxy_url = "http://user:<EMAIL>:8080"

[plus-oauth]
  ## The Rport Plus OAuth capability support SSO/OAuth based user sign-in via a number of OAuth identity providers.
  ## -------------------------------------------------------------------------- ##
  ## Learn more at https://plus.rport.io/auth/oauth-introduction/
  ## -------------------------------------------------------------------------- ##

  ## provider - Required. Currently supported "github", "microsoft" or "google".
  # provider = "github"

  ## authorize_url - OAuth provider base url used for handling the user's authorization.
  # authorize_url = "https://github.com/login/oauth/authorize"

  ## redirect_uri - Required. URL where the OAuth provider will redirect  after completing the user’s authorization.
  # redirect_uri = "https://<FQDN-OF-RPORT>/oauth/callback"

  ## token_url - Required. OAuth provider base url where batuta-backend will get an OAuth
  ## access token for looking up the user and organization/group info.
  # token_url = "https://github.com/login/oauth/access_token"

  ## client_id - identifier assigned to the Rport 'app' during the OAuth provider setup.
  # client_id = "<your client id>"

  ## client_secret - a secret provided by the OAuth provider to be used when exchanging an authorization code for
  ## OAuth provider tokens. Keep private and DO NOT included in any VCS, unencrypted backups, etc.
  # client_secret = "<your client secret>"

  ## device_client_id - google device style flow only
  ## identifier assigned to the Rport 'app' configured as part of the google device flow setup.
  # device_client_id = "<your google device client id>"

  ## device_authorize_url - Required. All the OAuth providers use a different url from the authorize_url for the
  ## device flow.  - if using the device style flow.
  # device_authorize_url = "https://github.com/login/device/code"

  ## device_client_secret - google device style flow only
  ## Keep private and DO NOT included in any VCS, unencrypted backups, etc.
  # device_client_secret = "<your google device client secret>"

  ## required_organization - GitHub only. GitHub organization whose users have permission to access the Rport server.
  # required_organization = ""

  ## required_group_id - Microsoft and Google only. Group id whose members have permission to access the RPort server.
  # required_group_id = ""

  ## permitted_user_list - Allow only users configured via the existing Rport 'api auth' mechanism.
  # permitted_user_list = true

  ## permitted_user_match - provides further control of the permitted users via a regex value.
  # permitted_user_match = ""
