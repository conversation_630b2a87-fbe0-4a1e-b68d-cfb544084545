#!/bin/bash

function main() {
  local jobs_deleted=""
  echo "$(date +'%Y-%m-%d %H:%M:%S.%3N') | Starting removal of temp jobs..."
  jobs_deleted=$(sqlite3 /var/lib/rport/jobs.db "DELETE FROM multi_jobs WHERE details LIKE '%audit-windows.ps1%' AND (started_at <= DATETIME('now', '-1 hour') OR started_at > DATETIME('now')); SELECT changes();")
  echo "$(date +'%Y-%m-%d %H:%M:%S.%3N') | Deleted $jobs_deleted compliance multi jobs"
  jobs_deleted=$(sqlite3 /var/lib/rport/jobs.db "DELETE FROM jobs WHERE details LIKE '%audit-windows.ps1%' AND (started_at <= DATETIME('now', '-1 hour') OR started_at > DATETIME('now')); SELECT changes();")
  echo "$(date +'%Y-%m-%d %H:%M:%S.%3N') | Deleted $jobs_deleted compliance jobs"
  jobs_deleted=$(sqlite3 /var/lib/rport/jobs.db "DELETE FROM multi_jobs WHERE details LIKE '%assetDiscovery%' AND (started_at <= DATETIME('now', '-1 hour') OR started_at > DATETIME('now')); SELECT changes();")
  echo "$(date +'%Y-%m-%d %H:%M:%S.%3N') | Deleted $jobs_deleted asset discovery multi jobs"
  jobs_deleted=$(sqlite3 /var/lib/rport/jobs.db "DELETE FROM jobs WHERE details LIKE '%assetDiscovery%' AND (started_at <= DATETIME('now', '-1 hour') OR started_at > DATETIME('now')); SELECT changes();")
  echo "$(date +'%Y-%m-%d %H:%M:%S.%3N') | Deleted $jobs_deleted asset discovery jobs"
  jobs_deleted=$(sqlite3 /var/lib/rport/jobs.db "DELETE FROM jobs WHERE multi_job_id IS NULL AND (started_at <= DATETIME('now', '-1 hour') OR started_at > DATETIME('now')); SELECT changes();")
  echo "$(date +'%Y-%m-%d %H:%M:%S.%3N') | Deleted $jobs_deleted common jobs"
  echo "$(date +'%Y-%m-%d %H:%M:%S.%3N') | Finished removal of temp jobs"
}

main >> /var/lib/rport/log/batuta-backend.log
