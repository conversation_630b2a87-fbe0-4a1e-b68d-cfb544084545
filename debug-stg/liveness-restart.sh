#!/bin/bash

readonly API_URL="http://localhost:3000/api/v1"
readonly API_AUTH=$(echo -n "soar:${RPORT_API_PREFIX}_${RPORT_API_KEY}" | base64 -w 0)
readonly API_HEADER="Authorization: Basic $API_AUTH"
readonly RESPONSE_TIME_LIMIT=10 # seconds
readonly CONNS_MIN_THRESHOLD=50
readonly CONNS_THRESHOLD_PERCENT=0.30 # acceptable percentage difference between connections and active endpoints

function main() {
  local current_datetime=$(date +"%Y-%m-%d_%H:%M:%S.%3N")
  local api_response_time=$(curl -o /dev/null -s -k -w "%{time_total}\n" -m 20 -H "$API_HEADER" $API_URL"/status")
  local api_conns=$(curl -k -s -m 5 -H "$API_HEADER" $API_URL"/clients?filter\[connection_state\]=connected" | jq -r ".meta.count")
  local system_conns=$(netstat -nt | grep ESTA | grep 80 | wc -l)
  local conns_threshold=$([ -z "$system_conns" ] && echo $CONNS_MIN_THRESHOLD || echo "scale=0; $system_conns * $CONNS_THRESHOLD_PERCENT / 1" | bc)
  local api_conns_with_threshold=$(expr $api_conns + $([ $conns_threshold -ge $CONNS_MIN_THRESHOLD ] && echo $conns_threshold || echo $CONNS_MIN_THRESHOLD))

  if [ $( echo "$api_response_time > $RESPONSE_TIME_LIMIT" | bc ) -ne 0 ] || ([ -n "$system_conns" ] && [ $system_conns -gt $api_conns_with_threshold ]); then
    echo "Restarting daemon at $current_datetime | api_response_time: $api_response_time and time_limit: $RESPONSE_TIME_LIMIT | api_conns: $api_conns and system_conns: $system_conns" >> /var/lib/rport/log/reincarnation.log
    exit 1
  else
    echo "NOT restarting daemon at $current_datetime | api_response_time: $api_response_time and time_limit: $RESPONSE_TIME_LIMIT | api_conns: $api_conns and system_conns: $system_conns" >> /var/lib/rport/log/reincarnation.log
  fi
}

main
